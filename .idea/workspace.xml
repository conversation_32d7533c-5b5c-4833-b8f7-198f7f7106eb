<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="72019321-6515-46d8-afce-cfe6306fa4fd" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/broker/model_manager_worker.py" beforeDir="false" afterPath="$PROJECT_DIR$/broker/model_manager_worker.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/database.py" beforeDir="false" afterPath="$PROJECT_DIR$/services/database.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Jupyter Notebook" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;QiaoooyiChen&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;**************:fhfuih/HKSI-Monitoring-Server.git&quot;,
    &quot;accountId&quot;: &quot;dfac0db7-5141-49ed-83f3-3aa83d80880e&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2qTZTMyWOSW7n7t0CefdgHmKX2J" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.main_hr.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/Desktop/Code/PyCharm/HKSI-Monitoring-Server&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="main_hr" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="HKSI-Monitoring-Server" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main_hr.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.main_hr" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e768b9ed790e-JavaScript-PY-243.21565.199" />
        <option value="bundled-python-sdk-cab1f2013843-4ae2d6a61b08-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.21565.199" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="72019321-6515-46d8-afce-cfe6306fa4fd" name="Changes" comment="" />
      <created>1734688532173</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1734688532173</updated>
      <workItem from="1734688533233" duration="649000" />
      <workItem from="1734691808530" duration="9580000" />
      <workItem from="1735550275249" duration="1777000" />
      <workItem from="1735744357827" duration="6264000" />
      <workItem from="1735770776870" duration="975000" />
      <workItem from="1735797893410" duration="14326000" />
      <workItem from="1736241150134" duration="3572000" />
      <workItem from="1736321904671" duration="2556000" />
      <workItem from="1736492397824" duration="1165000" />
      <workItem from="1736497549712" duration="20928000" />
      <workItem from="1736707361562" duration="2785000" />
      <workItem from="1736858035239" duration="1155000" />
      <workItem from="1736866231811" duration="43000" />
      <workItem from="1739361055099" duration="14424000" />
      <workItem from="1739437731214" duration="5520000" />
      <workItem from="1739902732826" duration="159000" />
      <workItem from="1739902902390" duration="2827000" />
      <workItem from="1740423157402" duration="1758000" />
      <workItem from="1740464830271" duration="8177000" />
      <workItem from="1741149651674" duration="22000" />
      <workItem from="1742405263025" duration="93000" />
      <workItem from="1742405651406" duration="4805000" />
      <workItem from="1742410985412" duration="675000" />
      <workItem from="1742749840840" duration="1581000" />
      <workItem from="1742790945085" duration="22122000" />
      <workItem from="1742842029223" duration="3823000" />
      <workItem from="1742873291501" duration="8979000" />
      <workItem from="1742902604879" duration="26392000" />
      <workItem from="1743614896197" duration="6876000" />
      <workItem from="1743927208404" duration="55332000" />
      <workItem from="1745130045306" duration="6941000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/HKSI_Monitoring_Server$main_hr.coverage" NAME="main_hr Coverage Results" MODIFIED="1734712125931" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>