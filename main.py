import argparse
import asyncio
import json
import logging
import os
import ssl
import sys
import time
import uuid
from pathlib import Path
from typing import Optional, cast

from dotenv import load_dotenv

from network.webrtc_session_manager import WebRTCSessionManager

load_dotenv()

# some ops are not available in macOS metal accelerator
# This allows fallback to CPU
os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"

import websockets
from aiohttp import web
from aiohttp_catcher import Catcher
from aiohttp_catcher.canned import AIOHTTP_SCENARIOS
from aiortc import (
    MediaStreamTrack,
    RTCConfiguration,
    RTCDataChannel,
    RTCIceServer,
    RTCPeerConnection,
    RTCSessionDescription,
    VideoStreamTrack,
)
from aiortc.contrib.media import (
    MediaBlackhole,
    MediaRecorder,
)
from aiortc.rtcrtpreceiver import RemoteStreamTrack
from av import logging as av_logging
from av.video.frame import VideoFrame

from broker import Broker
from models.base_model import BaseModel
from models.eye_bag_model import EyeBagModel
from models.face_rec_model import FaceRecognitionModel
from models.fatigue_model import FatigueModel
from models.mock_model_1 import MockModel1
from models.mock_model_2 import MockModel2
from models.Physiological import HeartRateAndHeartRateVariabilityModel
from models.pimple_model import PimpleModel
from services.database import DatabaseService
from utils.log import logger, set_console_log_level
from utils.network import ICE_SERVERS

ROOT = os.path.dirname(__file__)
# MODELS: list[type[BaseModel]] = [
#     FatigueModel,
#     EyeBagModel,
#     PimpleModel,
#     HeartRateAndHeartRateVariabilityModel,
#     FaceRecognitionModel,
# ]

MODELS: list[type[BaseModel]] = [MockModel1, MockModel2]

# WebRTC service
broker = Broker(MODELS)

# Command line arguments & globals to be initialized later
record_path = cast(Path, None)
webrtc_session_manager = cast(WebRTCSessionManager, None)


async def offer(request: web.Request) -> web.Response:
    try:
        params = await request.json()
        offer = RTCSessionDescription(sdp=params["sdp"], type=params["type"])
    except:
        try:
            text = await request.text()
            logger.debug(
                "Received invalid offer payload from %s: %s", request.remote, text
            )
        except UnicodeDecodeError:
            pass

        return web.Response(
            content_type="application/json",
            headers={"Acceppt": "application/json"},
            text=json.dumps({"sdp": None, "type": None}),
            status=400,
        )

    loop = asyncio.get_running_loop()

    pc_id, pc = await webrtc_session_manager.create_session(offer.sdp, "offer")
    logger.info("PC(%s) created for %s", pc_id, request.remote)

    logger.debug("Receiving remote SDP %s", pc.remoteDescription)
    logger.debug("Responding with local SDP %s", pc.localDescription)

    return web.json_response(
        {"sdp": pc.localDescription.sdp, "type": pc.localDescription.type}
    )


async def ice_servers(request: web.Request) -> web.Response:
    return web.json_response(ICE_SERVERS)


async def hello(request: web.Request) -> web.Response:
    logger.debug(f"Hello World from {request.remote}")
    return web.Response(text="Hello, world")


async def on_shutdown(app):
    return await webrtc_session_manager.cleanup()


async def websocket_handler(websocket):
    """Handle WebSocket connections for WebRTC signaling."""
    client_id = str(uuid.uuid4())[:8]
    logger.info(
        "New WebSocket connection from %s (ID: %s)", websocket.remote_address, client_id
    )

    try:
        async for message in websocket:
            try:
                data = json.loads(message)
                msg_type = data.get("type")

                if msg_type == "offer":
                    logger.info("Received offer from client %s", client_id)
                    offer_data = data.get("data", {})

                    # Create RTCSessionDescription from the offer
                    try:
                        offer = RTCSessionDescription(
                            sdp=offer_data["sdp"], type=offer_data["type"]
                        )
                    except (KeyError, TypeError) as e:
                        logger.error("Invalid offer format: %s", e)
                        await websocket.send(
                            json.dumps(
                                {
                                    "type": "error",
                                    "data": {
                                        "message": "Invalid offer format",
                                        "code": 400,
                                    },
                                }
                            )
                        )
                        continue

                    # Create peer connection
                    pc = RTCPeerConnection(
                        configuration=RTCConfiguration(
                            iceServers=[RTCIceServer(**ice) for ice in ICE_SERVERS]
                        )
                    )

                    pc_id = str(uuid.uuid4())
                    pcs.add(pc)

                    logger.info(
                        "PC(%s) created for WebSocket client %s", pc_id, client_id
                    )

                    # prepare local media
                    if record_path:
                        file_name = record_path / f"{pc_id}.mp4"
                        logger.info("PC(%s) recorded to %s", pc_id, file_name)
                        recorder = MediaRecorder(file_name)
                    else:
                        recorder = MediaBlackhole()

                    # get the main thread's event loop (For datachannel. It uses asyncio underneath)
                    loop = asyncio.get_running_loop()

                    @pc.on("datachannel")
                    def on_datachannel(channel: RTCDataChannel):  # type: ignore
                        logger.info(
                            "PC(%s) remote created datachannel %s", pc_id, channel.id
                        )

                        # Handle session-ending messages in the data channel
                        @channel.on("message")
                        def on_datachannel_message(message):
                            logger.info("PC(%s) received message: %s", pc_id, message)

                            if (
                                isinstance(message, str)
                                and message.strip() == "end session"
                            ):
                                # Mark session end
                                broker.end_session(pc_id)
                            else:
                                try:
                                    data = json.loads(message)
                                    # participant_id = None
                                    timestamp = int(
                                        time.time() * 1000
                                    )  # Current timestamp in milliseconds

                                    # Extract participant ID (After evaluation, we may not use participant id. Then, change here.)
                                    if "ParticipantID" in data.keys():
                                        participant_id = data["ParticipantID"]
                                        # person_id = data["PersonID"]
                                        del data["ParticipantID"]
                                        # print(f"Received ParticipantID: {participant_id}")
                                        if participant_id and not data:
                                            broker.set_participantID(participant_id)
                                            print(
                                                "broker.get_participantID(): ",
                                                broker.get_participantID(),
                                            )

                                        # Only process data if we have a participant ID (for evaluation, participant id and person id are different, so we must have not null participant id)
                                        elif participant_id:
                                            logger.info(
                                                f"Received ParticipantID and Related Messages: {participant_id} and {list(data.values())}"
                                            )
                                            person_id = data["PersonID"]
                                            logger.info(
                                                f"Received PersonID and Related Messages: {person_id} and {list(data.values())}"
                                            )
                                            # Initialize database connection if needed
                                            db = DatabaseService()

                                            # Store wellness data or body data
                                            if "surveyResult" in data:
                                                db.store_wellness_data_int(
                                                    person_id,
                                                    participant_id,
                                                    data["surveyResult"],
                                                    timestamp,
                                                )
                                                logger.info(
                                                    f"Stored survey data for participant {participant_id}"
                                                )
                                            elif "bodyDataDict" in data:
                                                db.store_wellness_data_float(
                                                    person_id,
                                                    participant_id,
                                                    data["bodyDataDict"],
                                                    timestamp,
                                                )
                                                logger.info(
                                                    f"Stored body data for participant {participant_id}"
                                                )
                                            elif "weightDataDict" in data:
                                                db.store_wellness_data_float(
                                                    person_id,
                                                    participant_id,
                                                    data["weightDataDict"],
                                                    timestamp,
                                                )
                                                logger.info(
                                                    f"Stored weight data for participant {participant_id}"
                                                )
                                    else:
                                        logger.info(
                                            "Received data without ParticipantID, cannot store any metrics"
                                        )

                                except json.JSONDecodeError:
                                    print("Failed to decode message as JSON.")
                                except Exception as e:
                                    logger.error(f"Error processing data: {str(e)}")
                                    # Send error back to client
                                    channel.send(
                                        json.dumps(
                                            {
                                                "status": "error",
                                                "message": f"Failed to process data: {str(e)}",
                                                "timestamp": int(time.time() * 1000),
                                            }
                                        )
                                    )

                        # Let broker emit prediction data through datachannel
                        async def send_data(data: dict):
                            if broker.get_participantID():
                                data["participant_id"] = broker.get_participantID()

                            # Create a copy of the data for logging purposes
                            log_data = data.copy() if data else {}
                            if "face_embedding" in log_data:
                                # Replace the long embedding list with a short placeholder string for logging
                                log_data["face_embedding"] = (
                                    f"[Embedding of size {len(log_data.get('face_embedding', []))}]"
                                )

                            logger.info("There is data sent from backend: %s", log_data)

                            d = json.dumps(
                                data,  # Send the original data, not the modified log_data
                                ensure_ascii=False,
                                default=lambda o: logger.error(f"can't serialize {o}")
                                or None,
                            )
                            channel.send(d)

                        def on_prediction(data: Optional[dict]):
                            if channel.readyState == "closed" or data is None:
                                return
                            asyncio.ensure_future(send_data(data), loop=loop)

                        broker.set_data_handler(pc_id, on_prediction, on_prediction)

                    @pc.on("track")
                    def on_track(track: MediaStreamTrack):  # type: ignore
                        logger.info(
                            "PC(%s) remote created %s track with id %s and type %s",
                            pc_id,
                            track.kind,
                            track.id,
                            type(track),
                        )

                        if track.kind == "audio":
                            # We don't care about audio. Just save it for future convenience
                            recorder.addTrack(track)

                        elif track.kind == "video":
                            # Should be this subclass accord
                            #
                            # ng to log. Cast for IDE code completion.
                            track = cast(RemoteStreamTrack, track)

                            # Mark session start
                            broker.start_session(pc_id)
                            logger.info(f"PC({pc_id}) model session started")

                            # This adds the track to the file writer
                            # NOTE: CANNOT USE `track` ever since creating a transformation
                            transformed_track = VideoTransformTrack(track, pc_id)
                            recorder.addTrack(transformed_track)
                            logger.debug(
                                f"PC({pc_id}) prediction and recording hooks are registered"
                            )

                            # Determine the start/stop of recorder based on the track
                            @track.on("ended")
                            async def on_ended():
                                logger.info(f"PC({pc_id}) video track {track.id} ended")
                                await recorder.stop()

                    # more event handlers and logging
                    @pc.on("connectionstatechange")
                    async def on_connectionstatechange():  # type: ignore
                        logger.info("PC(%s) -> %s", pc_id, pc.connectionState)
                        if pc.connectionState == "failed":
                            await pc.close()
                            pcs.discard(pc)

                    @pc.on("iceconnectionstatechange")
                    async def on_iceconnectionstatechange():
                        logger.debug(
                            "PC(%s) iceConnectionState->%s",
                            pc_id,
                            pc.iceConnectionState,
                        )

                    @pc.on("signalingstatechange")
                    async def on_signalingstatechange():
                        logger.debug(
                            "PC(%s) signalingState->%s", pc_id, pc.signalingState
                        )

                    @pc.on("icegatheringstatechange")
                    async def on_icegatheringstatechange():
                        logger.debug(
                            "PC(%s) iceGatheringState->%s", pc_id, pc.iceGatheringState
                        )

                    # handle offer
                    await pc.setRemoteDescription(offer)
                    await recorder.start()

                    # send answer
                    answer = await pc.createAnswer()
                    await pc.setLocalDescription(answer)  # type: ignore

                    # Send answer back through WebSocket
                    await websocket.send(
                        json.dumps(
                            {
                                "type": "answer",
                                "data": {
                                    "sdp": pc.localDescription.sdp,
                                    "type": pc.localDescription.type,
                                },
                            }
                        )
                    )

                elif msg_type == "ice-servers":
                    # Send ICE servers configuration
                    await websocket.send(
                        json.dumps({"type": "ice-servers", "data": ICE_SERVERS})
                    )

                else:
                    logger.warning("Unknown message type: %s", msg_type)
                    await websocket.send(
                        json.dumps(
                            {
                                "type": "error",
                                "data": {
                                    "message": f"Unknown message type: {msg_type}",
                                    "code": 400,
                                },
                            }
                        )
                    )

            except json.JSONDecodeError:
                logger.error("Invalid JSON message: %s", message)
                await websocket.send(
                    json.dumps(
                        {
                            "type": "error",
                            "data": {"message": "Invalid JSON message", "code": 400},
                        }
                    )
                )
            except Exception as e:
                logger.error("Error processing WebSocket message: %s", e)
                await websocket.send(
                    json.dumps(
                        {
                            "type": "error",
                            "data": {"message": f"Server error: {str(e)}", "code": 500},
                        }
                    )
                )

    except websockets.exceptions.ConnectionClosed:
        logger.info("WebSocket connection closed for client %s", client_id)
    finally:
        logger.info("WebSocket connection ended for client %s", client_id)


async def setupHttpServer() -> web.Application:
    catcher = Catcher()
    await catcher.add_scenarios(*AIOHTTP_SCENARIOS)

    app = web.Application(middlewares=[catcher.middleware])

    app.on_shutdown.append(on_shutdown)
    app.router.add_post(
        "/offer", offer
    )  # Keep HTTP endpoint for backward compatibility
    app.router.add_get("/", hello)
    app.router.add_get("/ice-servers", ice_servers)

    return app


if __name__ == "__main__":
    host = "127.0.0.1" if os.environ.get("ONLY_LOCALHOST") == "true" else "0.0.0.0"
    port = int(port_str) if (port_str := os.environ.get("PORT")) else 8083

    ssl_cert_file = os.environ.get("SSL_CERT_FILE")
    ssl_key_file = os.environ.get("SSL_KEY_FILE")
    ssl_cert_file = ssl_cert_file and ssl_cert_file.strip()
    ssl_key_file = ssl_key_file and ssl_key_file.strip()
    if ssl_cert_file and ssl_key_file:  # not None or empty string
        logger.info(f"SSL enabled.")
        ssl_context = ssl.SSLContext(protocol=ssl.PROTOCOL_TLS_SERVER)
        ssl_context.load_cert_chain(ssl_cert_file, ssl_key_file)
    else:
        logger.info(
            "SSL disabled because %s is empty."
            "But you can still configure SSL by putting this service behind a reverse proxy.",
            "SSL_CERT_FILE" if not ssl_cert_file else "SSL_KEY_FILE",
        )
        ssl_context = None

    parser = argparse.ArgumentParser(
        description="HKSI WebRTC server",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--record-to",
        default="./recordings",
        help="Write received media to a folder. Give it an empty string to disable",
    )
    parser.add_argument("--verbose", "-v", action="count")
    args = parser.parse_args()

    if record_to := args.record_to.strip():
        record_path = Path(record_to).resolve()
        if record_path.is_file():
            print(
                "The record-to path is a file. It should be a folder instead.",
                file=sys.stderr,
            )
            sys.exit(1)
        record_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"Saving video recordings to {record_path}")
    else:
        record_path = None
        logger.info(f"Don't save video recordings")

    if args.verbose:
        set_console_log_level(logging.DEBUG)
    av_logging.set_level(av_logging.ERROR)  # Slient internal logging of the av package

    # Launch core service components
    loop = asyncio.get_event_loop()
    webrtc_session_manager = WebRTCSessionManager(loop, record_path, broker)

    async def main():
        """Start the WebSocket server for signaling."""
        ws_port = port + 1
        logger.info(f"Starting WebSocket signaling server on {host}:{ws_port}")

        async with websockets.serve(
            websocket_handler,
            host,
            ws_port,
            ssl=ssl_context,
        ) as server:
            logger.info(
                f"WebSocket signaling server running on {'wss' if ssl_context else 'ws'}://{host}:{ws_port}"
            )
            await server.serve_forever()

    asyncio.run(main())
